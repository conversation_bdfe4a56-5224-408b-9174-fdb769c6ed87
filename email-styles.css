/* NK2R Competition Email Styles */
/* Science Journal Inspired Color Palette */

:root {
    --primary-purple: #b794f6;
    --light-purple: #e8d5ff;
    --primary-blue: #4299e1;
    --dark-blue: #3182ce;
    --text-dark: #2d3748;
    --text-medium: #4a5568;
    --text-light: #718096;
    --background-light: #f7fafc;
    --white: #ffffff;
    --error-bg: #fed7d7;
    --error-text: #c53030;
    --warning-bg: #feebc8;
    --warning-text: #c05621;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-medium);
    background-color: var(--background-light);
}

/* Email Container */
.email-container {
    max-width: 600px;
    margin: 20px auto;
    background-color: var(--white);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Header Gradient */
.header-gradient {
    background: linear-gradient(135deg, var(--light-purple) 0%, var(--primary-purple) 50%, var(--primary-blue) 100%);
    padding: 40px 30px;
    text-align: center;
}

.header-title {
    color: var(--white);
    font-size: 28px;
    font-weight: bold;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.header-subtitle {
    color: var(--white);
    font-size: 16px;
    margin: 10px 0 0 0;
    opacity: 0.95;
}

/* Content Sections */
.content-section {
    padding: 40px 30px;
}

.section-title {
    color: var(--text-dark);
    font-size: 22px;
    margin: 0 0 20px 0;
    border-bottom: 3px solid var(--primary-purple);
    padding-bottom: 10px;
}

.subsection-title {
    color: var(--text-dark);
    font-size: 20px;
    margin: 30px 0 20px 0;
}

/* Highlight Boxes */
.highlight-box {
    background-color: var(--background-light);
    padding: 25px;
    border-left: 4px solid var(--primary-blue);
    margin: 30px 0;
}

.challenge-grid {
    display: flex;
    gap: 30px;
    margin-top: 20px;
}

.challenge-item {
    flex: 1;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 15px;
}

.challenge-short-half {
    background-color: var(--error-bg);
}

.challenge-short-half h4 {
    color: var(--error-text);
    font-size: 16px;
    margin: 0 0 10px 0;
}

.challenge-short-half p {
    color: #742a2a;
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
}

.challenge-selectivity {
    background-color: var(--warning-bg);
}

.challenge-selectivity h4 {
    color: var(--warning-text);
    font-size: 16px;
    margin: 0 0 10px 0;
}

.challenge-selectivity p {
    color: #7c2d12;
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
}

/* Call to Action */
.cta-section {
    background: linear-gradient(135deg, var(--light-purple) 0%, var(--primary-purple) 100%);
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    margin: 30px 0;
}

.cta-title {
    color: var(--white);
    font-size: 22px;
    margin: 0 0 20px 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.cta-text {
    color: var(--white);
    font-size: 16px;
    line-height: 1.6;
    margin: 0 0 25px 0;
    opacity: 0.95;
}

.cta-button {
    display: inline-block;
    background-color: var(--white);
    color: #6b46c1;
    padding: 15px 30px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    font-size: 16px;
    transition: all 0.3s ease;
}

.cta-button:hover {
    background-color: var(--background-light);
    transform: translateY(-2px);
}

/* Lists */
.feature-list {
    list-style: none;
    padding-left: 0;
}

.feature-list li {
    margin-bottom: 10px;
    padding-left: 20px;
    position: relative;
}

.feature-list li:before {
    content: "•";
    color: var(--primary-purple);
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Footer */
.email-footer {
    background-color: var(--text-dark);
    padding: 30px;
    text-align: center;
}

.footer-text {
    color: #a0aec0;
    font-size: 14px;
    margin: 0 0 10px 0;
}

.footer-subtext {
    color: var(--text-light);
    font-size: 12px;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 600px) {
    .email-container {
        margin: 10px;
        width: calc(100% - 20px);
    }
    
    .content-section {
        padding: 20px 15px;
    }
    
    .header-gradient {
        padding: 30px 20px;
    }
    
    .header-title {
        font-size: 24px;
    }
    
    .header-subtitle {
        font-size: 14px;
    }
    
    .challenge-grid {
        flex-direction: column;
        gap: 15px;
    }
    
    .section-title {
        font-size: 20px;
    }
    
    .subsection-title {
        font-size: 18px;
    }
}
