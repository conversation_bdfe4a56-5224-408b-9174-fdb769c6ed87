<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NK2R竞赛邮件宣传预览</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f2f5;
            color: #333;
        }
        
        .preview-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .preview-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #e8d5ff 0%, #b794f6 50%, #4299e1 100%);
            border-radius: 10px;
            color: white;
        }
        
        .preview-section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-header {
            background-color: #2d3748;
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: bold;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .banner-preview {
            text-align: center;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .file-info {
            background-color: #e8f4fd;
            padding: 15px;
            border-left: 4px solid #4299e1;
            margin-bottom: 20px;
            border-radius: 0 5px 5px 0;
        }
        
        .file-info h4 {
            margin: 0 0 10px 0;
            color: #2d3748;
        }
        
        .file-info p {
            margin: 5px 0;
            color: #4a5568;
            font-size: 14px;
        }
        
        .email-frame {
            border: 1px solid #e2e8f0;
            border-radius: 5px;
            overflow: hidden;
            background: white;
        }
        
        .text-preview {
            background-color: #f7fafc;
            padding: 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .download-links {
            margin-top: 20px;
            text-align: center;
        }
        
        .download-link {
            display: inline-block;
            margin: 0 10px;
            padding: 10px 20px;
            background-color: #4299e1;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        
        .download-link:hover {
            background-color: #3182ce;
        }
        
        .specs-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .specs-table th,
        .specs-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .specs-table th {
            background-color: #f7fafc;
            font-weight: bold;
            color: #2d3748;
        }
        
        .status-check {
            color: #38a169;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="preview-header">
            <h1>NK2R多肽设计竞赛</h1>
            <h2>Science期刊邮件宣传材料预览</h2>
            <p>科研期刊风格 | 淡紫色到蓝色渐变设计</p>
        </div>
        
        <!-- HTML邮件预览 -->
        <div class="preview-section">
            <div class="section-header">HTML邮件预览</div>
            <div class="section-content">
                <div class="file-info">
                    <h4>📧 nk2r-competition-email.html</h4>
                    <p><strong>用途:</strong> 主要的HTML格式邮件内容</p>
                    <p><strong>特点:</strong> 内联CSS样式，响应式设计，科研期刊风格</p>
                    <p><strong>规格:</strong> 符合Science期刊HTML邮件要求，文件大小 < 500KB</p>
                </div>
                
                <div class="email-frame">
                    <iframe src="nk2r-competition-email.html" width="100%" height="600" frameborder="0"></iframe>
                </div>
                
                <table class="specs-table">
                    <tr>
                        <th>技术要求</th>
                        <th>状态</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>内联CSS样式</td>
                        <td class="status-check">✓ 完成</td>
                        <td>所有样式使用内联方式，确保兼容性</td>
                    </tr>
                    <tr>
                        <td>HTML表格布局</td>
                        <td class="status-check">✓ 完成</td>
                        <td>使用表格布局，适配各种邮件客户端</td>
                    </tr>
                    <tr>
                        <td>响应式设计</td>
                        <td class="status-check">✓ 完成</td>
                        <td>适配桌面和移动设备</td>
                    </tr>
                    <tr>
                        <td>无JavaScript</td>
                        <td class="status-check">✓ 完成</td>
                        <td>纯HTML/CSS实现</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- 纯文本邮件预览 -->
        <div class="preview-section">
            <div class="section-header">纯文本邮件预览</div>
            <div class="section-content">
                <div class="file-info">
                    <h4>📄 nk2r-competition-email.txt</h4>
                    <p><strong>用途:</strong> 纯文本版本的邮件内容</p>
                    <p><strong>特点:</strong> 结构清晰，适用于不支持HTML的邮件客户端</p>
                </div>
                
                <div class="text-preview">NK2R多肽设计竞赛
神经激肽-2受体：治疗代谢疾病的双效多肽药物设计新靶点

===============================================

背景：代谢疾病治疗的迫切需求

全球范围内，肥胖、糖尿病等代谢性疾病的发病率持续攀升。应对这些疾病的一个有效策略是将"减少食物摄入"与"增加能量消耗"相结合...

[点击查看完整文本内容]</div>
            </div>
        </div>
        
        <!-- 横幅广告预览 -->
        <div class="preview-section">
            <div class="section-header">横幅广告预览 (468×60px)</div>
            <div class="section-content">
                <div class="file-info">
                    <h4>🎯 nk2r-banner-ad.html</h4>
                    <p><strong>用途:</strong> Science官网和邮件快讯的横幅广告</p>
                    <p><strong>规格:</strong> 468×60像素，< 100KB，包含悬停效果</p>
                </div>
                
                <div class="banner-preview">
                    <iframe src="nk2r-banner-ad.html" width="500" height="100" frameborder="0"></iframe>
                </div>
                
                <table class="specs-table">
                    <tr>
                        <th>规格要求</th>
                        <th>状态</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>尺寸: 468×60px</td>
                        <td class="status-check">✓ 符合</td>
                        <td>标准横幅广告尺寸</td>
                    </tr>
                    <tr>
                        <td>文件大小 < 100KB</td>
                        <td class="status-check">✓ 符合</td>
                        <td>HTML/CSS实现，体积小</td>
                    </tr>
                    <tr>
                        <td>包含跳转链接</td>
                        <td class="status-check">✓ 完成</td>
                        <td>点击跳转到报名页面</td>
                    </tr>
                    <tr>
                        <td>视觉效果</td>
                        <td class="status-check">✓ 完成</td>
                        <td>悬停效果和渐变背景</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- 下载链接 -->
        <div class="preview-section">
            <div class="section-header">文件下载</div>
            <div class="section-content">
                <p>所有文件已准备就绪，可直接用于Science期刊发布：</p>
                <div class="download-links">
                    <a href="nk2r-competition-email.html" class="download-link" download>HTML邮件</a>
                    <a href="nk2r-competition-email.txt" class="download-link" download>纯文本邮件</a>
                    <a href="nk2r-banner-ad.html" class="download-link" download>横幅广告</a>
                    <a href="README.md" class="download-link" download>使用说明</a>
                </div>
                
                <div class="file-info" style="margin-top: 30px;">
                    <h4>📋 提交清单</h4>
                    <p>✓ HTML邮件文件 (.html)</p>
                    <p>✓ 纯文本邮件文件 (.txt)</p>
                    <p>✓ 横幅广告文件 (.html)</p>
                    <p>✓ 完整的使用说明文档</p>
                    <p>✓ 符合Science期刊所有技术规范</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
